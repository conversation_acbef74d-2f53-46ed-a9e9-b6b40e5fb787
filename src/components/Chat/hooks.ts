/* eslint-disable max-lines */
import {useEffect, useLayoutEffect} from 'react';
import {useLocation, useNavigate, useParams} from 'react-router-dom';
import {useRequest} from 'huse';
import {head} from 'lodash';
import {
    useCurrentNotificationApp,
    useCurrentChatId,
    getCurrentChatId,
    useCurrentChatOpen,
} from '@/regions/staff/chatSdk';
import {apiGetAgentById, apiGetChatList} from '@/api/staff';
import {usePaginationSearchObj} from '@/hooks/ievalue/dashboard';
import {
    getAgentList,
    useAgentList,
    useCurrentAgentId,
} from '@/regions/staff/agent';
import {
    createChatWebsocket,
    createNotificationWebsocket,
} from '@/regions/staff/websocketRefs';
import {
    changeConversationId,
    changeFilterSelectHeadChat,
} from '@/regions/staff/chatHandlers';
import {resetAllStage, setChat, useCurrentChat} from '@/regions/staff/chat';
import {DEFAULT_TASK_ID} from '@/constants/staff';

export function useChatId() {
    const {chatId} = useParams();
    if (!chatId) {
        throw new Error('chatId is required');
    }
    return chatId;
}

export function useAgentId() {
    const {agentId} = useParams();
    if (!agentId) {
        throw new Error('agentId is required');
    }
    return +agentId;
}

export function useChatWebsocketEffect() {
    const currentChatId = useCurrentChatId();
    // 这里是自动触发的，唯一能触发这个 effect 的就是去 setCurrentChatId
    useEffect(
        () => {
            let ws = null;
            if (currentChatId) {
                ws = createChatWebsocket(currentChatId);
            }
            return () => {
                resetAllStage();
                if (ws) {
                    ws.close();
                }
            };
        },
        [currentChatId]
    );
}

export function useChatIdEffect() {
    const currentChatId = useCurrentChatId();
    // 这里是自动触发的，唯一能触发这个 effect 的就是去 setCurrentChatId
    useEffect(
        () => {
            if (currentChatId) {
                changeConversationId(currentChatId).catch(console.error);
            }
        },
        [currentChatId]
    );
}

export function useOpenChatEffect() {
    const open = useCurrentChatOpen();
    useEffect(
        () => {
            const agentList = getAgentList();
            const currentChatId = getCurrentChatId();
            if (!currentChatId) {
                changeFilterSelectHeadChat({
                    agentId: head(agentList)?.id,
                });
            }
        },
        [open]
    );
}

export function useNotificationWebsocketEffect() {
    const app = useCurrentNotificationApp();
    // 这里是自动触发的，唯一能触发这个 effect 的就是去 setCurrentNotificationApp
    useEffect(
        () => {
            let ws = null;
            if (app) {
                ws = createNotificationWebsocket(app);
            }
            return () => {
                if (ws) {
                    ws.close();
                }
            };
        },
        [app]
    );
}

const convertData = (data: string[]) => {
    return data?.length ? data?.join(',') : undefined;
};

export function useChatListPaginationList() {
    const {
        pn: page,
        size,
        setPn,
        setSize,
        searchObj,
        setSearchObj,
    } = usePaginationSearchObj({}, 20);
    const {timeRange, buildStatus, agentId, delegationType, adoptStatus, ...others} =
        searchObj;
    const {
        data: chatListRes,
        pending,
        refresh,
    } = useRequest(apiGetChatList, {
        page,
        size,
        ...others,
        buildStatus: convertData(buildStatus),
        agentId: convertData(agentId),
        delegationType: convertData(delegationType),
        adoptStatus: convertData(adoptStatus),
        ...(timeRange
            ? {startTime: timeRange[0], endTime: timeRange[1]}
            : {}),
    });

    return {
        dataSource: chatListRes?.data ?? [],
        pagination: {
            current: page,
            pageSize: size,
            total: chatListRes?.total ?? 0,
            onChange: (page: number, pageSize: number) => {
                setSize(pageSize);
                setPn(page);
            },
            showSizeChanger: true,
        },
        refresh,
        pending,
        searchObj,
        setSearchObj,
    };
}

export function useAgentInfo() {
    const id = useCurrentAgentId();
    const {data: agentInfo, refresh} = useRequest(apiGetAgentById, {
        id,
    });

    return [agentInfo, refresh] as const;
}

export function useAgentInfoById(agentId: number) {
    const agentList = useAgentList();
    return agentList?.find(x => x.id === agentId);
}

export function useAnchorAssociation() {
    const location = useLocation();
    const navigate = useNavigate();
    const currentChatId = useCurrentChatId();

    useLayoutEffect(
        () => {
            if (!location?.search) {
                return;
            }
            const urlParams = new URLSearchParams(location?.search);
            const taskId = urlParams.get('taskId');
            const stageId = urlParams.get('stageId');
            const tabId = urlParams.get('tabId');
            const stepId = urlParams.get('stepId');
            if (currentChatId && (stageId || tabId || stepId || taskId)) {
                setChat(currentChatId, item => ({
                    ...item,
                    currentTaskId: taskId ?? DEFAULT_TASK_ID,
                    currentStageId: stageId,
                    currentTabId: tabId,
                    currentStepId: stepId,
                }));
                const timer = setTimeout(() => {
                    navigate(location.pathname, {replace: true});
                }, 3000);
                return () => clearTimeout(timer);
            }
        },
        [currentChatId, location, navigate]
    );
}

export function useStepIdAssociation() {
    const {currentStepId, conversationId} = useCurrentChat();
    useLayoutEffect(
        () => {
            if (currentStepId) {
                const timer = setTimeout(() => {
                    const element = document.getElementById(currentStepId);
                    if (element) {
                        element.scrollIntoView({behavior: 'smooth'});
                    }
                    setChat(conversationId, item => ({
                        ...item,
                        currentStepId: null,
                    }));
                }, 300);
                return () => clearTimeout(timer);
            }
        },
        [conversationId, currentStepId]
    );
}
